package com.haoxueren.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.os.Vibrator;
import android.util.AttributeSet;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.haoxueren.helper.PageLoadHelper;
import com.haoxueren.pray.bean.HaoPray;
import com.haoxueren.pray.R;
import com.haoxueren.pray.main.MainPresenter;
import com.haoxueren.proxy.RecyclerViewProxy;
import com.haoxueren.proxy.SuperViewHolder;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.constant.RefreshState;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.function.Consumer;

@SuppressLint("CheckResult")
public class PrayRecordWidget extends FrameLayout {

    SmartRefreshLayout refreshLayout;
    RecyclerViewProxy<HaoPray> recyclerView;

    private String prayId = "";
    private final int size = 20;
    private List<HaoPray> recordList = new ArrayList<>();
    private MainPresenter presenter = MainPresenter.getInstance();
    private PageLoadHelper<HaoPray> loadHelper = new PageLoadHelper<>(recordList, size);

    public PrayRecordWidget(@NonNull Context context) {
        this(context, null);
    }

    public PrayRecordWidget(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        View.inflate(context, R.layout.widget_pray_record, this);
        refreshLayout = findViewById(R.id.refreshLayout);
        recyclerView = new RecyclerViewProxy<HaoPray>(this, R.id.recyclerView) {
            @Override
            protected SuperViewHolder<HaoPray> onCreateHolder(ViewGroup parent, int viewType) {
                return new SuperViewHolder<HaoPray>(parent, R.layout.item_pray_record) {

                    TextView recordTextView;

                    @Override
                    public void initView(View layout) {
                        recordTextView = itemView.findViewById(R.id.recordTextView);
                        itemView.setOnCreateContextMenuListener((menu, itemView, menuInfo) -> {
                            // 初始化上下文菜单
                            int position = getBindingAdapterPosition();
                            menu.setHeaderTitle(getAdapterList().get(position).getId());
                            MenuItem deleteItem = menu.add(0, 1, 1, "删除");
                            // 使用Intent携带数据
                            Intent intent = new Intent();
                            intent.putExtra("position", position);
                            deleteItem.setIntent(intent);
                        });

                        // 添加长按监听器显示详情对话框
                        itemView.setOnLongClickListener(v -> {
                            int position = getBindingAdapterPosition();
                            if (position != -1 && position < getAdapterList().size()) {
                                HaoPray record = getAdapterList().get(position);

                                // 添加震动反馈
                                Vibrator vibrator = (Vibrator) getContext().getSystemService(Context.VIBRATOR_SERVICE);
                                if (vibrator != null && vibrator.hasVibrator()) {
                                    vibrator.vibrate(50); // 震动50毫秒
                                }

                                // 显示详情对话框
                                PrayRecordDetailDialog.show(getContext(), record);
                            }
                            return true; // 返回true表示消费了长按事件
                        });
                    }

                    @Override
                    public void updateItem(HaoPray bean) {
                        String record = String.format(Locale.CHINESE, "%s.%d %s", bean.getDate(), bean.getCount(), bean.getPray());
                        recordTextView.setText(record);
                    }
                };
            }
        };
        recyclerView.setAdapter(recordList);
        initRefreshLayout();
    }

    private void initRefreshLayout() {
        refreshLayout.setOnRefreshListener(layout -> {
            loadHelper.onRefresh((page, size) -> {
                loadRecord(prayId, 0);
            });
        });
        refreshLayout.setOnLoadMoreListener(layout -> {
            if (loadHelper.hasMoreData()) {
                loadHelper.onLoadMore((page, size) -> {
                    loadRecord(prayId, (page - 1) * size);
                });
            } else {
                refreshLayout.finishLoadMoreWithNoMoreData();
            }
        });
    }

    public void loadRecord(String id, int skip) {
        this.prayId = id;
        presenter.queryRecord(id, skip, size)
                .subscribe(this::onRecordSuccess, this::onBmobFailure);
    }

    public void deleteRecord(int position) {
        HaoPray haoPray = recordList.get(position);
        presenter.deletePrayRecord(haoPray).subscribe(
                onDeleteSuccess(position)::accept, this::onBmobFailure);
    }

    public Consumer<String> onDeleteSuccess(int position) {
        recordList.remove(position);
        recyclerView.notifyItemRemoved(position);
        Toast.makeText(getContext(), "删除成功", Toast.LENGTH_SHORT).show();
        return System.out::println;
    }

    private void onRecordSuccess(List<HaoPray> list) {
        RefreshState state = refreshLayout.getState();
        loadHelper.onSuccess(list, state == RefreshState.Loading, () -> {
            recyclerView.notifyDataSetChanged();
            refreshLayout.finishRefresh();
            refreshLayout.finishLoadMore();
        });
    }

    public void onBmobFailure(Throwable e) {
        e.printStackTrace();
        refreshLayout.finishRefresh();
        refreshLayout.finishLoadMore();
        Toast.makeText(getContext(), e.getMessage(), Toast.LENGTH_SHORT).show();
    }

    public void scrollToTop() {
        recyclerView.scrollToPosition(0);
    }
}
