package com.haoxueren.widget

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.haoxueren.pray.R
import com.haoxueren.pray.bean.HaoPray
import com.haoxueren.pray.service.DataService
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers

/**
 * 祈祷记录详情对话框
 * 使用 BottomSheetDialog 实现从底部向上弹出的效果
 */
@SuppressLint("CheckResult")
class PrayRecordDetailDialog(
    private val context: Context,
    private val record: HaoPray
) {
    
    private lateinit var dialog: BottomSheetDialog
    private lateinit var groupIdTextView: TextView
    private lateinit var prayIdTextView: TextView
    private lateinit var dateTextView: TextView
    private lateinit var countTextView: TextView
    private lateinit var prayContentTextView: TextView
    private lateinit var objectIdTextView: TextView
    private lateinit var createdAtTextView: TextView
    private lateinit var updatedAtTextView: TextView
    private lateinit var closeButton: ImageView
    
    fun show() {
        // 使用主题感知的BottomSheetDialog
        dialog = BottomSheetDialog(context, R.style.BottomSheetDialogTheme)
        val view = LayoutInflater.from(context).inflate(R.layout.dialog_pray_record_detail, null)
        dialog.setContentView(view)

        // 设置底部弹窗为全屏宽度
        dialog.setOnShowListener { dialogInterface ->
            val bottomSheetDialog = dialogInterface as BottomSheetDialog
            val bottomSheet = bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.let {
                val layoutParams = it.layoutParams
                layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT
                it.layoutParams = layoutParams
            }
        }

        initViews(view)
        setupCloseButton()
        loadRecordDetails()

        dialog.show()
    }
    
    private fun initViews(view: View) {
        groupIdTextView = view.findViewById(R.id.groupIdTextView)
        prayIdTextView = view.findViewById(R.id.prayIdTextView)
        dateTextView = view.findViewById(R.id.dateTextView)
        countTextView = view.findViewById(R.id.countTextView)
        prayContentTextView = view.findViewById(R.id.prayContentTextView)
        objectIdTextView = view.findViewById(R.id.objectIdTextView)
        createdAtTextView = view.findViewById(R.id.createdAtTextView)
        updatedAtTextView = view.findViewById(R.id.updatedAtTextView)
        closeButton = view.findViewById(R.id.closeButton)
    }
    
    private fun setupCloseButton() {
        closeButton.setOnClickListener {
            dialog.dismiss()
        }
        
        // 点击外部区域关闭对话框
        dialog.setCanceledOnTouchOutside(true)
    }
    
    private fun loadRecordDetails() {
        // 设置基本信息
        prayIdTextView.text = record.id ?: "未知"
        dateTextView.text = record.date ?: "未知"
        countTextView.text = record.count?.toString() ?: "0"
        prayContentTextView.text = record.pray ?: "无内容"
        objectIdTextView.text = record.objectId ?: "未知"
        createdAtTextView.text = record.createdAt ?: "未知"
        updatedAtTextView.text = record.updatedAt ?: "未知"
        
        // 异步获取 groupId
        loadGroupId()
    }
    
    private fun loadGroupId() {
        val prayId = record.id
        if (prayId.isNullOrEmpty()) {
            groupIdTextView.text = "未知"
            return
        }
        
        DataService.getInstance().getGroupId(prayId)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(
                { groupId ->
                    groupIdTextView.text = groupId?.toString() ?: "未分组"
                },
                { error ->
                    error.printStackTrace()
                    groupIdTextView.text = "获取失败"
                }
            )
    }
    
    companion object {
        /**
         * 显示记录详情对话框
         * @param context 上下文
         * @param record 要显示的记录
         */
        @JvmStatic
        fun show(context: Context, record: HaoPray) {
            val dialog = PrayRecordDetailDialog(context, record)
            dialog.show()
        }
    }
}
